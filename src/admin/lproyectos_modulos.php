<?php

declare(strict_types=1);

use App\classes\ProyectoModulo;
use App\classes\Proyecto;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion; // Make sure $conexion is available globally or passed appropriately

// Adjust paths as per your project structure
require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Set timezone for date operations
date_default_timezone_set('America/Bogota');

#region region Flash Messages
$success_display = 'none';
$success_text    = '';
$error_display   = 'none';
$error_text      = '';

// Check for flash messages from session
if (isset($_SESSION['flash_message_success'])) {
	$success_display = 'show';
	$success_text    = $_SESSION['flash_message_success'];
	unset($_SESSION['flash_message_success']);
}

if (isset($_SESSION['flash_message_error'])) {
	$error_display = 'show';
	$error_text    = $_SESSION['flash_message_error'];
	unset($_SESSION['flash_message_error']);
}
#endregion Flash Messages

#region region Handle POST Actions (Create, Update, Delete)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
	try {
		$action = $_POST['action'];
		
		if ($action === 'crear') {
			$id_proyecto = filter_input(INPUT_POST, 'id_proyecto', FILTER_VALIDATE_INT);
			$descripcion = trim($_POST['descripcion'] ?? '');
			
			if (!$id_proyecto) {
				throw new Exception("ID de proyecto inválido.");
			}
			
			if (empty($descripcion)) {
				throw new Exception("La descripción del módulo no puede estar vacía.");
			}
			
			// Verify project exists
			$proyecto = Proyecto::get($id_proyecto, $conexion);
			if (!$proyecto) {
				throw new Exception("El proyecto seleccionado no existe.");
			}
			
			$modulo = new ProyectoModulo();
			$modulo->setIdProyecto($id_proyecto);
			$modulo->setDescripcion($descripcion);
			
			if ($modulo->guardar($conexion)) {
				$_SESSION['flash_message_success'] = "Módulo creado correctamente.";
			} else {
				throw new Exception("No se pudo crear el módulo.");
			}
		} elseif ($action === 'modificar') {
			$id_modulo   = filter_input(INPUT_POST, 'id_modulo', FILTER_VALIDATE_INT);
			$id_proyecto = filter_input(INPUT_POST, 'id_proyecto', FILTER_VALIDATE_INT);
			$descripcion = trim($_POST['descripcion'] ?? '');
			
			if (!$id_modulo) {
				throw new Exception("ID de módulo inválido.");
			}
			
			if (!$id_proyecto) {
				throw new Exception("ID de proyecto inválido.");
			}
			
			if (empty($descripcion)) {
				throw new Exception("La descripción del módulo no puede estar vacía.");
			}
			
			// Get existing module
			$modulo = ProyectoModulo::get($id_modulo, $conexion);
			if (!$modulo) {
				throw new Exception("No se encontró el módulo con ID $id_modulo.");
			}
			
			// Verify project exists
			$proyecto = Proyecto::get($id_proyecto, $conexion);
			if (!$proyecto) {
				throw new Exception("El proyecto seleccionado no existe.");
			}
			
			// Update properties
			$modulo->setIdProyecto($id_proyecto);
			$modulo->setDescripcion($descripcion);
			
			// Save changes
			if ($modulo->guardar($conexion)) {
				$_SESSION['flash_message_success'] = "Módulo actualizado correctamente.";
			} else {
				throw new Exception("No se pudo actualizar el módulo.");
			}
		} elseif ($action === 'desactivar') {
			$id_modulo = filter_input(INPUT_POST, 'id_modulo_desactivar', FILTER_VALIDATE_INT);
			
			if (!$id_modulo) {
				throw new Exception("ID de módulo inválido para desactivar.");
			}
			
			$modulo = ProyectoModulo::get($id_modulo, $conexion);
			if (!$modulo) {
				throw new Exception("No se encontró el módulo con ID $id_modulo.");
			}
			
			if ($modulo->cambiarEstado($conexion, 0)) {
				$_SESSION['flash_message_success'] = "Módulo desactivado correctamente.";
			} else {
				throw new Exception("No se pudo desactivar el módulo.");
			}
		} elseif ($action === 'activar') {
			$id_modulo = filter_input(INPUT_POST, 'id_modulo_activar', FILTER_VALIDATE_INT);
			
			if (!$id_modulo) {
				throw new Exception("ID de módulo inválido para activar.");
			}
			
			$modulo = ProyectoModulo::get($id_modulo, $conexion);
			if (!$modulo) {
				throw new Exception("No se encontró el módulo con ID $id_modulo.");
			}
			
			if ($modulo->cambiarEstado($conexion, 1)) {
				$_SESSION['flash_message_success'] = "Módulo activado correctamente.";
			} else {
				throw new Exception("No se pudo activar el módulo.");
			}
		} else {
			$_SESSION['flash_message_error'] = "Error: Acción no válida.";
		}
	} catch (Exception $e) {
		error_log("Error procesando acción '{$_POST['action']}' para ProyectoModulo: " . $e->getMessage());
		$_SESSION['flash_message_error'] = "Error: " . $e->getMessage();
	}
	
	// Redirect back to the modules list page after processing
	header('Location: lproyectos_modulos');
	exit;
}
#endregion Handle POST Actions

#region Fetch Data
try {
	// Get filter parameters
	$filtro_proyecto = filter_input(INPUT_GET, 'filtro_proyecto', FILTER_VALIDATE_INT);
	$filtro_descripcion = trim($_GET['filtro_descripcion'] ?? '');

	// Apply filters - only load data if at least one filter is provided
	if ($filtro_proyecto || !empty($filtro_descripcion)) {
		$modulos = ProyectoModulo::getFiltered($conexion, $filtro_proyecto, $filtro_descripcion);
	} else {
		// No filters applied, return empty array
		$modulos = [];
	}

	// Get all active projects for dropdown
	$proyectos = Proyecto::get_list($conexion);

} catch (PDOException $e) {
	error_log("Database error fetching modules: " . $e->getMessage());
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de módulos.";
} catch (Exception $e) {
	error_log("Error fetching modules: " . $e->getMessage());
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de módulos: " . $e->getMessage();
}
#endregion Fetch Data

require_once __ROOT__ . '/views/admin/lproyectos_modulos.view.php'; // Include the view
