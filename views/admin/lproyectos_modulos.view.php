<?php
/** @var PDO $conexion */
/** @var string $success_display */
/** @var string $success_text */
/** @var string $error_display */
/** @var string $error_text */
/** @var App\classes\ProyectoModulo[] $modulos */
/** @var App\classes\Proyecto[] $proyectos */

use App\classes\ProyectoModulo;
use App\classes\Proyecto;
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8" />
	<title>Gestión de Módulos de Proyectos | <?php echo APP_NAME; ?></title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
	<meta content="Administración de módulos de proyectos" name="description" />
	<meta content="" name="author" />

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Módulos de Proyectos</h4>
				<p class="mb-0 text-muted">Administra los módulos asociados a proyectos</p>
			</div>
			<div class="ms-auto">
				<button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createModuloModal">
					<i class="fa fa-plus-circle fa-fw me-1"></i> Crear nuevo
				</button>
			</div>
		</div>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region FILTROS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">Filtros de Búsqueda</h4>
			</div>
			<div class="panel-body">
				<form method="GET" action="lproyectos_modulos" class="row g-3">
					<div class="col-md-6">
						<label for="filtro_proyecto" class="form-label">Proyecto</label>
						<select class="form-select" id="filtro_proyecto" name="filtro_proyecto">
							<option value="">Seleccionar proyecto</option>
							<?php foreach ($proyectos as $proyecto): ?>
								<option value="<?php echo $proyecto->getId(); ?>"
								        <?php echo (isset($_GET['filtro_proyecto']) && $_GET['filtro_proyecto'] == $proyecto->getId()) ? 'selected' : ''; ?>>
									<?php echo htmlspecialchars($proyecto->getDescripcion() ?? ''); ?>
								</option>
							<?php endforeach; ?>
						</select>
					</div>
					<div class="col-md-6">
						<label for="filtro_descripcion" class="form-label">Descripción</label>
						<input type="text" class="form-control" id="filtro_descripcion" name="filtro_descripcion"
						       placeholder="Buscar por descripción..."
						       value="<?php echo htmlspecialchars($_GET['filtro_descripcion'] ?? ''); ?>">
					</div>
					<div class="col-12">
						<button type="submit" class="btn btn-primary me-2">
							<i class="fa fa-search me-1"></i> Aplicar Filtros
						</button>
						<a href="lproyectos_modulos" class="btn btn-secondary">
							<i class="fa fa-times me-1"></i> Limpiar Filtros
						</a>
					</div>
				</form>
			</div>
		</div>
		<?php #endregion FILTROS ?>

		<?php #region region PANEL MODULOS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">Módulos de Proyectos</h4>
				<div class="panel-heading-btn">
					<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
					<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
				</div>
			</div>
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region TABLE MODULOS ?>
				<?php
				$filtro_proyecto = $_GET['filtro_proyecto'] ?? '';
				$filtro_descripcion = $_GET['filtro_descripcion'] ?? '';
				$hay_filtros = !empty($filtro_proyecto) || !empty(trim($filtro_descripcion));
				?>

				<?php if (!$hay_filtros): ?>
					<div class="alert alert-info text-center">
						<i class="fa fa-info-circle me-2"></i>
						Aplique filtros para ver resultados
					</div>
				<?php elseif (empty($modulos)): ?>
					<div class="alert alert-warning text-center">
						<i class="fa fa-exclamation-triangle me-2"></i>
						No se encontraron módulos que coincidan con los filtros aplicados
					</div>
				<?php else: ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th class="text-center">Acciones</th>
							<th>Proyecto</th>
							<th>Descripción</th>
						</tr>
						</thead>
						<tbody class="fs-12px" id="modulo-table-body">
						<?php foreach ($modulos as $modulo): ?>
							<tr data-modulo-id="<?php echo $modulo->getId(); ?>"
								data-id-proyecto="<?php echo $modulo->getIdProyecto(); ?>"
								data-descripcion="<?php echo htmlspecialchars($modulo->getDescripcion() ?? ''); ?>">
								<td class="text-center">
									<?php // Edit Button - Triggers Modal ?>
									<button type="button" class="btn btn-xs btn-primary me-1 btn-edit-modulo"
									        title="Editar Módulo"
									        data-bs-toggle="modal"
									        data-bs-target="#editModuloModal">
										<i class="fa fa-edit"></i>
									</button>
									<?php // Activate/Deactivate Button ?>
									<?php if ($modulo->getEstado() == 1): ?>
										<button type="button" class="btn btn-xs btn-danger btn-desactivar-modulo"
										        title="Desactivar Módulo">
											<i class="fa fa-trash-alt"></i>
										</button>
									<?php else: ?>
										<button type="button" class="btn btn-xs btn-success btn-activar-modulo"
										        title="Activar Módulo">
											<i class="fa fa-check"></i>
										</button>
									<?php endif; ?>
								</td>
								<td><?php echo htmlspecialchars($modulo->getNombreProyecto() ?? 'N/A'); ?></td>
								<td><?php echo htmlspecialchars($modulo->getDescripcion() ?? ''); ?></td>
							</tr>
						<?php endforeach; ?>
						</tbody>
					</table>
				<?php endif; ?>
				<?php #endregion TABLE MODULOS ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL MODULOS ?>
	</div>
	<!-- END #content -->

	<?php #region Modals ?>
	<!-- Create Modulo Modal -->
	<div class="modal fade" id="createModuloModal" tabindex="-1" aria-labelledby="createModuloModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<form method="POST" action="lproyectos_modulos">
					<input type="hidden" name="action" value="crear">
					<div class="modal-header">
						<h5 class="modal-title" id="createModuloModalLabel">Crear Nuevo Módulo</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="mb-3">
							<label for="id_proyecto" class="form-label">Proyecto <span class="text-danger">*</span></label>
							<select class="form-select" id="id_proyecto" name="id_proyecto" required>
								<option value="">Seleccione un proyecto</option>
								<?php foreach ($proyectos as $proyecto): ?>
									<option value="<?php echo $proyecto->getId(); ?>">
										<?php echo htmlspecialchars($proyecto->getDescripcion() ?? ''); ?>
									</option>
								<?php endforeach; ?>
							</select>
						</div>
						<div class="mb-3">
							<label for="descripcion" class="form-label">Descripción <span class="text-danger">*</span></label>
							<input type="text" class="form-control" id="descripcion" name="descripcion" required>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="submit" class="btn btn-primary">Guardar</button>
					</div>
				</form>
			</div>
		</div>
	</div>

	<!-- Edit Modulo Modal -->
	<div class="modal fade" id="editModuloModal" tabindex="-1" aria-labelledby="editModuloModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<form method="POST" action="lproyectos_modulos">
					<input type="hidden" name="action" value="modificar">
					<input type="hidden" name="id_modulo" id="edit-modulo-id">
					<div class="modal-header">
						<h5 class="modal-title" id="editModuloModalLabel">Editar Módulo</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="mb-3">
							<label for="edit_id_proyecto" class="form-label">Proyecto <span class="text-danger">*</span></label>
							<select class="form-select" id="edit_id_proyecto" name="id_proyecto" required>
								<option value="">Seleccione un proyecto</option>
								<?php foreach ($proyectos as $proyecto): ?>
									<option value="<?php echo $proyecto->getId(); ?>">
										<?php echo htmlspecialchars($proyecto->getDescripcion() ?? ''); ?>
									</option>
								<?php endforeach; ?>
							</select>
						</div>
						<div class="mb-3">
							<label for="edit_descripcion" class="form-label">Descripción <span class="text-danger">*</span></label>
							<input type="text" class="form-control" id="edit_descripcion" name="descripcion" required>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="submit" class="btn btn-primary">Guardar cambios</button>
					</div>
				</form>
			</div>
		</div>
	</div>

	<!-- Deactivate Modulo Form (Hidden) -->
	<form id="desactivar-modulo-form" method="POST" action="lproyectos_modulos" style="display: none;">
		<input type="hidden" name="action" value="desactivar">
		<input type="hidden" name="id_modulo_desactivar" id="id_modulo_desactivar">
	</form>

	<!-- Activate Modulo Form (Hidden) -->
	<form id="activar-modulo-form" method="POST" action="lproyectos_modulos" style="display: none;">
		<input type="hidden" name="action" value="activar">
		<input type="hidden" name="id_modulo_activar" id="id_modulo_activar">
	</form>
	<?php #endregion Modals ?>

	<!-- BEGIN scroll-top-btn -->
	<a href="javascript:;" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Use event delegation on the table body
        const tableBody = document.getElementById('modulo-table-body');
        const editModal = new bootstrap.Modal(document.getElementById('editModuloModal'));
        const editFormIdInput = document.getElementById('edit-modulo-id');
        const editFormProyectoSelect = document.getElementById('edit_id_proyecto');
        const editFormDescripcionInput = document.getElementById('edit_descripcion');

        // Only add event listeners if table body exists (when there are results)
        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const editButton = event.target.closest('.btn-edit-modulo');
                const desactivarButton = event.target.closest('.btn-desactivar-modulo');
                const activarButton = event.target.closest('.btn-activar-modulo');

                // --- Handle Edit Click ---
                if (editButton) {
                    event.preventDefault();
                    const row = editButton.closest('tr');
                    const moduloId = row.dataset.moduloId;
                    const idProyecto = row.dataset.idProyecto;
                    const descripcion = row.dataset.descripcion;

                    if (editFormIdInput) editFormIdInput.value = moduloId;
                    if (editFormProyectoSelect) editFormProyectoSelect.value = idProyecto;
                    if (editFormDescripcionInput) editFormDescripcionInput.value = descripcion;

                    // The data-bs-toggle and data-bs-target attributes on the button handle showing the modal
                }

                // --- Handle Deactivate Click ---
                if (desactivarButton) {
                    event.preventDefault();
                    const row = desactivarButton.closest('tr');
                    const moduloId = row.dataset.moduloId;
                    const descripcion = row.dataset.descripcion;

                    handleActionConfirm(
                        moduloId,
                        descripcion,
                        'desactivar',
                        'Desactivar Módulo',
                        `¿Estás seguro de que deseas desactivar el módulo "${descripcion}"?`,
                        'desactivar-modulo-form',
                        'id_modulo_desactivar'
                    );
                }

                // --- Handle Activate Click ---
                if (activarButton) {
                    event.preventDefault();
                    const row = activarButton.closest('tr');
                    const moduloId = row.dataset.moduloId;
                    const descripcion = row.dataset.descripcion;

                    handleActionConfirm(
                        moduloId,
                        descripcion,
                        'activar',
                        'Activar Módulo',
                        `¿Estás seguro de que deseas activar el módulo "${descripcion}"?`,
                        'activar-modulo-form',
                        'id_modulo_activar',
                        'btn-success'
                    );
                }
            });
        }

        // --- Confirmation Function ---
        function handleActionConfirm(id, name, action, title, text, formId, inputId, confirmButtonClass = 'btn-danger') {
            swal({
                title: title,
                text: text,
                icon: "warning",
                buttons: {
                    cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                    confirm: {text: "Confirmar", value: true, visible: true, className: confirmButtonClass, closeModal: true}
                },
                dangerMode: (action === 'desactivar'), // Only danger mode for deactivate
            })
                .then((willConfirm) => {
                    if (willConfirm) {
                        document.getElementById(inputId).value = id;
                        document.getElementById(formId).submit();
                    }
                });
        }

        // No DataTables initialization to match ltransacciones.view.php style
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>
